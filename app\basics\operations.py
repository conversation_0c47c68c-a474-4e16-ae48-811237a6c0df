import io
import zipfile
import tkinter
import re
from tkinter import messagebox
from app.utils.utils import *
from app.basics.communicate import *

logger = logging.getLogger(__name__)

def start_download_execution(result_file, output_path):
    logger.info(f'Reading records from result file.')
    data = read_excel_to_dict_list(result_file)
    OPERATOR = get_operator_settings()
    SETTINGS_INFO = load_app_settings_file()
    error_count = 0
    envs_master = None
    if SETTINGS_INFO['envsOverWrite'] == "True":
        envs_master = read_envsOrverWrite_master("./master/envsOverWriteMaster.csv")
        if envs_master == None:
            return 1
    
    if len(data) == 0:
        logger.error(f"No record found in the excel document.")
        return 1

    for i, row in enumerate(data):
        try:
            global_number = row["グローバル番号"]
            spec_id = row["スペックID"]
        except:
            logger.error("There are no required columns (GlobalNumber or SpecID) in the input file")
            return 1
        try:
            logger.info(f'RECORD-{i+1} :: WORKING DATA: GlobalNumber: {global_number}, Spec_Id: {spec_id}')
            order_info = download_order_info(global_number, spec_id)
            if order_info == 1:
                raise Exception("target search error in meviy")

            category_folder_name = get_division_folder_name(row, "category")
            subcategory_folder_name = get_division_folder_name(row, "sub")
            orderDetailId = get_orderDetailId(order_info)
            if spec_id is None:
                spec_id =  order_info['specId'] #row['スペックID']がnullだったときの対策（SOのみ発生する可能性を想定）

            status_sat = download_satfile(orderDetailId, category_folder_name, subcategory_folder_name, output_path, spec_id)
            status_wisdom = download_wisdomfile(orderDetailId, category_folder_name, subcategory_folder_name, output_path, spec_id, envs_master)

            if status_wisdom == 0:
                sort_unused_envs_files(category_folder_name, subcategory_folder_name, output_path)
                
            if OPERATOR != None and SETTINGS_INFO['QTorSO'] == 'QT' :
                status_drawing = download_drawingfile_qt(orderDetailId, category_folder_name, subcategory_folder_name, output_path, spec_id)
            else:
                status_drawing = download_drawingfile(orderDetailId, category_folder_name, subcategory_folder_name, output_path, spec_id)
            
            if status_sat == 1 or status_wisdom == 1 or status_drawing == 1:
                raise Exception("file download error")
            
        except Exception as ex:
            error_count += 1
            logger.error(f"{str(ex)} - GlobalNumber: {global_number}, Spec_Id: {spec_id}")
    if error_count > 0:
        logger.error(f"{error_count}件のダウンロードファイルにエラーがあります")
        if OPERATOR != None:
            root = tkinter.Tk()
            root.attributes('-topmost', True)
            root.withdraw()
            messagebox.showerror('Error', f"{error_count}件のダウンロードファイルにエラーがあります") 

    return 0 

        

def download_and_save(id, filetype, specId, savepath):
    res = download_from_meviy(id, filetype)
    if res.status_code == 200:
        if not os.path.exists(savepath):
            os.makedirs(savepath)

        extension = get_savefile_extension(filetype)

        if get_zip_file_content_for_sat(res) == False:
            filename = f'{specId}.{extension}'
            file_path = os.path.join(savepath, filename)
            with open(file_path, 'wb') as file:
                file.write(res.content)
            return 0
        else:
            zip_file = zipfile.ZipFile(io.BytesIO(res.content))
            zip_file.extractall(savepath)
            extracted_dir = os.path.join(savepath, specId)
            copy_directory_contents(extracted_dir, savepath)
            delete_directory(extracted_dir)
            #ファイル名を変更(operator権限でDLしたファイルに必要)
            files = os.listdir(savepath)
            for file in files:
                if file.endswith('.' + extension):
                    original_file_path = os.path.join(savepath, file)
                    new_file_path = os.path.join(savepath, specId + '.' + extension)
                    os.rename(original_file_path, new_file_path)
            
            return 0
    return 1

def download_and_save_wisdom_files(id, filetype, spec_id, savepath):
    res = download_from_meviy(id, filetype)
    if res.status_code == 200:
        if not os.path.exists(savepath):
            os.makedirs(savepath)

        zip_file = zipfile.ZipFile(io.BytesIO(res.content))
        zip_file.extractall(savepath)
        if get_operator_settings() is None:
            extracted_dir = os.path.join(savepath, "WISDOM")
            copy_directory_contents(extracted_dir, savepath)
            delete_directory(extracted_dir)
        else:
            extracted_dir = os.path.join(savepath, spec_id, "WISDOM")
            copy_directory_contents(extracted_dir, savepath)
            delete_directory(extracted_dir)
            delete_directory(os.path.join(savepath, spec_id))
        return 0
    return 1

def envs_over_write(master, savepath):
    status = 0
    logpath = savepath.replace("\\envs", "")
    with open(os.path.join(logpath, "envsOverWrite.log"), "w") as log_file:
        log_file.write(f"envs over write log - {logpath}.\n")

        # 指定ディレクトリ内のファイルを読み込み、修正
        for filename in os.listdir(savepath):
            overwrite = False
            try:
                if filename.startswith('feature_') and filename.endswith('.json'):
                    file_path = os.path.join(savepath, filename)
                    data = read_json_file(file_path)
                    
                    feature_type = data.get("featureType", None)
                    for mas in master:
                        target_check = envs_target_check(mas, data, feature_type)
                        
                        if target_check:
                            mode = mas["Mode"]
                            before_val = data["env"].get(mas["OverWriteKey"], None)
                            after_val = mas["OverWriteValue"]

                            if mode == "Append":
                                if before_val != None:
                                    data["env"][mas["OverWriteKey"]] = float(after_val)
                                    log_file.write(f"{filename} : [Append] {mas['OverWriteKey']} changed {before_val} → {after_val}\n")
                                    overwrite = True
                                else:
                                    raise Exception(f"[Append] Not found OverWriteKey : {mas['OverWriteKey']}\n")
                            elif mode == "Calculation":
                                if before_val is None:
                                    raise Exception(f"[Calculation] Not found OverWriteKey : {mas['OverWriteKey']}\n")
                                if not float(before_val):
                                    raise Exception(f"[Calculation] OverWriteKey : {mas['OverWriteKey']} value is not number.\n")
                                
                                if "+" in after_val:
                                    val = float(before_val) + float(after_val[1:])
                                    data["env"][mas["OverWriteKey"]] = val
                                elif "-" in after_val:
                                    val = float(before_val) - float(after_val[1:])
                                    data["env"][mas["OverWriteKey"]] = val
                                log_file.write(f"{filename} : [Calculation] {mas['OverWriteKey']} calculated {before_val} → {val}\n")
                                overwrite = True
                            elif mode == "Add":
                                if before_val is None:
                                    try:
                                        float(after_val)
                                        after_val = float(after_val)
                                    except:
                                        pass
                                    data["env"][mas["OverWriteKey"]] = after_val
                                    log_file.write(f"{filename} : [Add] {mas['OverWriteKey']}:{after_val}\n")
                                else:
                                    log_file.write(f"{filename} : [Add] {mas['OverWriteKey']} already exists.\n")
                                overwrite = True
                                

                    #修正したJSONをファイルに書き戻し
                    if overwrite :
                        with open(file_path, 'w', encoding='utf-8') as file:
                            json.dump(data, file, separators=(',', ':'))
                
            except Exception as ex:
                log_file.write(f"Error : {filename} - {ex} .\n")
                status = 1
    if status == 1:
        logger.error(f"envs over write error - Please check envsOverWrite.log ")
    return status

def envs_target_check(mas, data, feature_type):
    target_check = False
    if feature_type == mas["featureType"]:
        shape_type = data["env"].get(mas["ShapeTypeKey"], None)
        if shape_type == mas["ShapeTypeValue"] :
            # マスタの"TargetKey-〇" カラム(書き換え対象のファイルを絞るカラム)は無限に作成可能な想定
            target_cols = [key for key in mas if "TargetKey" in key]
            for targetcol in target_cols:
                match = re.search( r"-\d+", targetcol)
                if match :
                    match = match.group()
                    mas_target_val = mas[f"TargetValue" + match]
                else:
                    mas_target_val = mas[f"TargetValue"]
                if mas_target_val == "" or mas_target_val == None:
                    continue
                try:
                    float(mas_target_val)
                    mas_target_val = float(mas_target_val)
                except:
                    pass
                
                data_target_val =  data["env"].get(mas[targetcol], None) 
                if mas_target_val == data_target_val:
                    target_check = True
                elif mas_target_val == "none":
                    if data_target_val == None:
                        target_check = True
                else:
                    target_check = False
                    break
    return target_check


def download_satfile(orderDetailId, folder_name, subfolder_name, output_path, spec_id):
    sat_output_path =  os.path.join(output_path, folder_name, subfolder_name)
    status = download_and_save(orderDetailId, "OriginChangeAndColoredSat", spec_id, sat_output_path)
    if status == 0:
        logger.info(f"DOWNLOAD COMPLETE:: SAT file")
    return status

def download_wisdomfile(orderDetailId, folder_name, subfolder_name, output_path, spec_id, envs_master):
    wisdom_output_path = os.path.join(output_path, folder_name, subfolder_name, "envs")
    status = download_and_save_wisdom_files(orderDetailId, "WisdomJson", spec_id, wisdom_output_path)
    if status == 0:
        if envs_master != None:
            status = envs_over_write(envs_master, wisdom_output_path)
        if status == 0:
            logger.info(f"DOWNLOAD COMPLETE:: WISDOM file")
    return status

def download_drawingfile(orderDetailId, folder_name, subfolder_name, output_path, spec_id):
    drawing_output_path =  os.path.join(output_path, folder_name, subfolder_name)
    status = download_and_save(orderDetailId, "SimpleTwoDTranslucentPdfWithHeaderForInspecting", spec_id,  drawing_output_path)
    if status == 0:
        logger.info(f"DOWNLOAD COMPLETE:: Inspection Drawing file")
    return status

def download_drawingfile_qt(orderDetailId, folder_name, subfolder_name, output_path, spec_id):
    drawing_output_path =  os.path.join(output_path, folder_name, subfolder_name)
    status = download_and_save(orderDetailId, "SimpleTwoDTranslucentPdfWithHeader", spec_id,  drawing_output_path)
    if status == 0:
        logger.info(f"DOWNLOAD COMPLETE:: Inspection Drawing file")
    return status

# def download_envs_file(session, orderDetailId, folder_name, specId, output_path):
#     envs_output_path = os.path.join(output_path, folder_name, specId)
#     res = download_from_meviy(session, orderDetailId, "Envs")
#     if res.status_code == 200:
#         with zipfile.ZipFile(io.BytesIO(res.content)) as thezip:
#             thezip.extractall(path=envs_output_path)
#             logger.info(f"DOWNLOAD COMPLETE:: ENVS file")
#             return 0
#     return 1

def download_order_info(globalnumber, specID):
    order_info = get_meviy_order_info(globalnumber, specID)
    if order_info == None or order_info == 1:
        return 1
    
    try: #SO
        if order_info['items'] == 0 or order_info['items'] == []:
            logger.error(f"can't find the data for GlobalnNumber = {globalnumber} , SpecID = {specID} from the list on SO")
            return 1
        else:
            if order_info['total'] > 1:
                sorted_data = sorted(order_info['items'], key=lambda x: datetime.strptime(x['confirmedShipmentDate'], "%Y%m%d"), reverse=True)
                info = sorted_data[0]
            else:
                info = order_info['items'][0]
    except: #QT
        if  order_info['salesQuotationRelation'] == 0 or order_info['salesQuotationRelation'] == []:
            logger.error(f"can't find the data for GlobalnNumber = {globalnumber} , SpecID = {specID}  from the list on QT")
            return 1
        else:
            info = order_info['salesQuotationRelation'][0]
    
    logger.info(f"Gettiing Order Infomation...COMPLETED")
    return info
    

def sort_unused_envs_files(folder_name, subfolder_name, output_path):
    wisdom_file_path = os.path.join(output_path, folder_name, subfolder_name, "envs", "WISDOM.json")
    envs_folder_path = os.path.join(output_path, folder_name, subfolder_name, "envs")
    try:
        wisdom_data = read_json_file(wisdom_file_path)
        del_envs = wisdom_data.get('del_envs')
        if del_envs is not None:
            if len(del_envs) == 0:
                return
            envs_files = read_all_feature_files(envs_folder_path)
            for feature_file in envs_files:
                feature_data = read_json_file(feature_file)
                feature_id = feature_data.get('env', {}).get('@FEATURE.ID')
                if feature_id in del_envs:
                    base_spec_path = os.path.join(output_path, folder_name, subfolder_name)
                    move_file_to_no_use_folder(base_spec_path, feature_file)

    except Exception as ex:
        logger.error(str(ex))

