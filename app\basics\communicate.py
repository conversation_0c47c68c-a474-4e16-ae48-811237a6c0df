# import io
import time
import requests
# import math
# import zipfile
from app.utils.utils import *

logger = logging.getLogger(__name__)

def create_session(login_config_file=None):
    global config_file
    SETTINGS_INFO = load_app_settings_file()

    if SETTINGS_INFO is None:
        logger.error("Settings.json not found!!")
    
    if login_config_file != None:
        config_file = login_config_file
    

    if not os.path.exists(config_file):
        logger.error("Login config file not found!!")
        return None
        
    with open(config_file, 'r', newline="",encoding="utf-8") as f:
        LOGIN_INFO = json.load(f)

    LOGIN_INFO = create_login_param_body(LOGIN_INFO)
    if LOGIN_INFO is None:
        logger.error("Login info not found!!")
    
    headers = {
        'Content-Type': 'application/json'
        }

    session = requests.Session()
    res = session.post(SETTINGS_INFO['login_url'], json=LOGIN_INFO, headers=headers)

    if res.status_code == 200:
        check_effective_session(0, session)
        return session
    
    return None

def check_effective_session(flag, session=None):
    global effective_session
    if flag == 0: 
        effective_session = session
    if flag == 1:
        return effective_session


def create_login_param_body(login_info):
    login_params = {}
    try:
        login_params['id'] = login_info['id']
        login_params['password'] = login_info['password']
        login_params['remFlg'] = bool(login_info['remFlg'])
    except:
        return None
    return login_params

def get_meviy_order_info(globalnumber, specID):
    OPERATOR = get_operator_settings()
    SETTINGS_INFO = load_app_settings_file()
    if OPERATOR is None:
        if globalnumber == None or str(globalnumber) == "nan":
            logger.error("The GlobalNo cell has no value in the input file")
            return 1
        else:
            headers = {
                'Content-Type': 'application/json'
                }
            
            data = {
                "supplierOrderStatus": [],
                "orderStatus": [],
                "globalNo": globalnumber,
                "heatTreatments": []
            }       
            return send_request(SETTINGS_INFO['order_url'], headers, data, globalnumber, specID) 
    else:
        if SETTINGS_INFO['QTorSO'] == 'SO':
            key = SETTINGS_INFO['download_key']

            headers = {
            'Content-Type': 'application/json'
            }
        
            data = {
                "orderResponsiblePersonStatus":[],
                "orderResponsiblePersonId":[],
                "isNoneOrderResponsiblePersonId":False,
                "serviceType":[],
                "supplierIds":[],
                "isNotPrivateCompany":False,
                "productType":[]
            }
            
            if "グローバル番号" in key:
                if  globalnumber == None or str(globalnumber) == "nan": 
                    logger.error("The GlobalNo cell has no value in the input file" )
                    return 1
                else:
                    data["globalNo"] = globalnumber
            if "スペックID" in key:
                if specID == None or str(specID) == "nan": 
                    logger.error("The SpecID cell has no value in the input file" )
                    return 1
                else:
                    data["specId"] = specID
            if len(key)==0 or ("グローバル番号" not in key and "スペックID" not in key):
                logger.error("Error getting value of 'download_key' in setting file" )
                return 1
            
            return send_request(SETTINGS_INFO['order_url'], headers, data, globalnumber, specID)
            
        elif SETTINGS_INFO['QTorSO'] == 'QT':
            headers = {
            'Content-Type': 'application/json'
            }
        
            data = {
                "subsidiaryCodes":[],
                "customerSubsidiaryCodes":[],
                "responsiblePersonIds":[],
                "responsibleStatuses":[],
                "isUnassigned":False,
                "serviceType":[],
                "isExcludeOwnCode":False,
                "isFixedSpecification":False,
                "quotationPattern":[],
                "specIds": [specID]
            }
            
            return send_request(SETTINGS_INFO['order_url_qt'], headers, data, globalnumber, specID)

        else:
            logger.error("Error getting value of 'QTorSO' in setting file" )
            return 1
    


def send_request(order_url, headers, data, globalnumber, specID):
    session = check_effective_session(1)
    try:
        for retries_count in range(3):
            request = requests.Request('POST', order_url, headers=headers, json=data)
            prepared_request = session.prepare_request(request)
            res = session.send(prepared_request)

            if res.status_code == 200:
                return res.json()
            else:
                if res.status_code == 401 and "tokenexpired" in res.text.lower():  
                    retries_count += 1 #タイムアウトによるトークンエラーのときだけ、3回までトライする想定
                    logger.error("Error: Token expired")    
                    session = create_session()
                    if session is None: #sessionがNoneのときは違うエラーが想定されるので終了
                        raise Exception("Authentication Failed!! Check your credentials.")
                else:
                    raise Exception(f"status code = {res.status_code}, text = {res.text}")
    except Exception as ex:
        logger.error(f"Error getting order information for GlobalNumber: {globalnumber}, SpecID: {specID}. {str(ex)}")
    
    return 1

def get_zip_file_content_for_sat(response):
    if 'application/zip' in response.headers.get('Content-Type', '') or 'application/octet-stream' in response.headers.get('Content-Type', ''):
        if response.content[:2] == b'PK':
            return True
    return False

def download_from_meviy(id, filetype):
    session = check_effective_session(1)
    SETTINGS_INFO = load_app_settings_file()
    OPERATOR = get_operator_settings()

    params = {
        'id': id,
        'fileType': filetype
    }

    # extension = get_savefile_extension(filetype)
    if OPERATOR != None and SETTINGS_INFO['QTorSO'] == 'QT':
        URL = SETTINGS_INFO['download_url_qt']
    else:
        URL = SETTINGS_INFO['download_url']
    for attempt in range(5):
        try:
            
            request = requests.Request('GET', URL, params=params)
            prepared_request = session.prepare_request(request)
            res = session.send(prepared_request)

            if res.status_code == 200:
                if len(res.content) == 0:
                    #QTではsatファイル等がまだ作成されていない場合があり、そのときはリクエストAPIを受け付けてから作成開始される
                    #ファイル完成前にDLして空のファイルとなる場合がある
                    raise Exception(f"empty file : filetype={filetype}")
                else:
                    return res
            else:
                if res.status_code == 401 and "tokenexpired" in res.text.lower():  
                    # ログインから一定時間経過するとトークンエラーになるため、再接続する  
                    session = create_session()
                    if session is None: 
                        raise Exception("Authentication Failed!! Check your credentials.")
                else:
                    raise Exception(f"status code = {res.status_code}, text = {res.text}")
        except Exception as ex:
            if attempt < 5:
                time.sleep(1)
            else:
                logger.error(f"downloading file : {str(ex)} , Abort retry.")

    return 1