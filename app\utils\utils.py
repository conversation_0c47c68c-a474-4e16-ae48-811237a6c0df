
import os
import json
import glob
import csv
import shutil
import logging
import re
import pandas as pd
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler

def initialize_log(additional_handlers=None):
    settings = load_app_settings_file()
    if settings is None:
        return 1

    log_folder = ensure_log_folder(settings['log_path'])
    log_file = 'application.log'
    level = logging.INFO

    # Log format
    LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
    LOG_DATEFMT = "%Y-%m-%d %H:%M:%S"

    # Ensure log folder exists
    if not os.path.exists(log_folder):
        os.makedirs(log_folder)

    # Create handlers
    stream_handler = logging.StreamHandler()
    file_handler = TimedRotatingFileHandler(os.path.join(log_folder, log_file), encoding="utf-8-sig", when="midnight", interval=1, backupCount=30)

    handlers = [stream_handler, file_handler]
    if additional_handlers:
        handlers.extend(additional_handlers)

    logging.basicConfig(format=LOG_FORMAT, datefmt=LOG_DATEFMT, level=level, handlers=handlers)

    return logging

def get_operator_settings():
    app_settings_path = f'./settings'
    filepath = os.path.join(app_settings_path, "settings_operator.json")
    if os.path.exists(filepath):
        return filepath
    else:
        return None
    


def load_app_settings_file():
    logger = logging.getLogger()
    app_settings_path = f'./settings'
    #_operatorファイルが存在すればそちらを優先
    filepath = get_operator_settings()
    if filepath is None:
        filepath = os.path.join(app_settings_path, "settings.json")
    try:
        with open(filepath, 'r', encoding='utf-8-sig') as file:
            return json.load(file)
    except json.JSONDecodeError as e:
        logger.error(f"Settings file JSONDecodeError: {e.msg} (line {e.lineno} column {e.colno})")
        return None
    except Exception as e:
        logger.error(e)
        return None
    
def ensure_log_folder(path):
    if os.path.basename(path) == 'log':
        return path
    else:
        return os.path.join(path, 'log')
    
def get_savefile_extension(filetype):
    if filetype.upper() == "OriginChangeAndColoredSat".upper():
        return "sat"
    if filetype.upper() == "WisdomJson".upper():
        return "json"
    if filetype.upper() == "Envs".upper():
        return "zip"
    if filetype.upper() == "SimpleTwoDTranslucentPdfWithHeaderForInspecting".upper():
        return "pdf"
    if filetype.upper() == "SimpleTwoDTranslucentPdfWithHeader".upper():
        return "pdf"
    return None

def read_excel_to_dict_list(file_path):
    try:
        df = pd.read_excel(file_path, sheet_name=0)
        data_dict_list = df.to_dict(orient='records')
        return data_dict_list
    
    except FileNotFoundError:
        return {"error": "File not found. Please check the file path and try again."}
    
    except ValueError as ve:
        return {"error": f"ValueError: {ve}. Please check the file and sheet name."}
    
    except Exception as e:
        return {"error": f"An unexpected error occurred: {e}"}
    
def read_json_file(file_path):
    try:
        with open(file_path, 'r') as file:
            data = json.load(file)
            return data
    except:
        return None
    
def read_all_feature_files(envs_folder):
    file_pattern = os.path.join(envs_folder, 'feature*')
    return glob.glob(file_pattern)

def move_file_to_no_use_folder(spec_path, file_path):
    no_use_path = os.path.join(spec_path, "NoUseENVS")
    if not os.path.exists(no_use_path):
        os.makedirs(no_use_path)
    
    try:
        filename_with_extension = os.path.basename(file_path)
        move_file_path = os.path.join(no_use_path, filename_with_extension)
        shutil.move(file_path, move_file_path)
    except:
        pass

def copy_directory_contents(src, dst):
    if os.path.exists(src):
        for item in os.listdir(src):
            src_item = os.path.join(src, item)
            dst_item = os.path.join(dst, item)
            
            if os.path.isdir(src_item):
                if os.path.exists(dst_item):
                    shutil.rmtree(dst_item)
                shutil.copytree(src_item, dst_item)
            else:
                shutil.copy2(src_item, dst_item)

def delete_directory(directory_path):
    shutil.rmtree(directory_path)

def get_division_folder_name(datarow, flag):
    settings = load_app_settings_file()
    if flag=="category":
        mapping_column = settings.get('category_foldername_column_mapping')
    elif flag=="sub":
        mapping_column = settings.get('subcategory_foldername_column_mapping')
    folder_name = ""
    if mapping_column != None:
        for column in mapping_column:
            column_value = datarow.get(column)
            if column_value == None or str(column_value) == "nan":
                # folder_name = str(folder_name) + "None" + "_"
                pass
            else:
                folder_name = str(folder_name) + str(column_value) + "_"

    if re.fullmatch(f"({'None_'})+", folder_name) is not None:
        return "noName"
    elif folder_name != "":
        return folder_name[:-1]
    else:
        return "noName"


def get_orderDetailId(order_info):
    OPERATOR = get_operator_settings()
    if OPERATOR is None :
        return order_info['orderDetailId']
    else:
        SETTINGS_INFO = load_app_settings_file()
        if SETTINGS_INFO['QTorSO'] == 'QT' :
            return order_info['qtConditionId']
        else:
            return order_info['id']
        

def read_envsOrverWrite_master(path):
    logger = logging.getLogger()
    encodings = ['utf-8-sig', 'utf-8', 'shift-jis', 'cp932']
    message = None
    for encoding in encodings:
        try:
            with open(path, mode='r', encoding=encoding) as csvfile:
                reader = csv.DictReader(csvfile)
                rows = [row for row in reader]
                spaces = []   
                sign = []
                row_index = 0
                for row in rows:
                    row_index += 1
                    for key, val in row.items():
                        if any(char.isspace() for char in val):  # スペースやタブ等空白が含まれているかチェック
                            spaces.append((key, row_index)) 
                    if row["Mode"] == "Calc":
                        if row["OverWriteValue"][0] not in ('+', '-') :
                            sign.append(row_index) 
                if spaces:
                    for key, row_index in spaces:
                        logger.error(f"envsOverWrite.csv has spaces :(column-'{key}', row-{str(row_index)})")
                if sign:
                    for row_index in sign:
                        logger.error(f"envsOverWrite.csv '+' or '-' is required for Calc mode : row-{str(row_index)}")
                if spaces or sign:
                    raise MasterError("")  

                return rows
            
        except FileNotFoundError:
            message = "FileNotFoundError"
            break  # No need to try other encodings if file doesn't exist
        except PermissionError:
            message = "PermissionError"
            break  # Similarly, no need to continue if we don't have permission
        except UnicodeDecodeError:
            message = "UnicodeDecodeError"
            pass
        except MasterError:
            return None
        except Exception as e:
            message = e
            pass
    logger.error("envsOverWrite master error : " + message)
    return None

class MasterError(Exception):
    """カスタムエラークラス"""
    def __init__(self, message):
        super().__init__(message)
