
def styleSubmitButton(executeButton):
        executeButton.setStyleSheet("""
            QPushButton {
                    background-color: #0095ff;
                    border: 1px solid transparent;
                    border-radius: 3px;
                    color: #fff;
                    font-size: 15px;
                    font-weight: 400;
                    line-height: 1.15385;
                    margin: 0;
                    outline: none;
                    padding: 8px .8em;
                    position: relative;
                    text-align: center;
                    text-decoration: none;
                    vertical-align: baseline;
                    white-space: nowrap;
            }
            QPushButton:hover {
                background-color: #07c;
            }
        """)

def styleTextEdit(textEdit):
        textEdit.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #cccccc;
                border: 1px solid #555555;
            }
        """)