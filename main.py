import sys
import os
import j<PERSON>
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QW<PERSON>t, QLabel, QLineEdit, QTextEdit , QPushButton, QMessageBox, QGridLayout, QFileDialog, QDesktopWidget
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QMetaObject, Q_ARG, QTimer
from PyQt5.QtGui import QFont, QTextCursor

from app.basics.controller import do_operation
from app.utils.utils import *
from app.styles.style import *

class QTextEditLogger(logging.Handler):
    def __init__(self, parent):
        super().__init__()
        self.widget = QTextEdit(parent)
        self.widget.setFont(QFont('Courier', 9))
        self.widget.setReadOnly(True)
        styleTextEdit(self.widget)

    def emit(self, record):
        msg = self.format(record)
        QMetaObject.invokeMethod(self.widget, "append", Qt.QueuedConnection, Q_ARG(str, msg))
        QTimer.singleShot(0, self.scrollToBottom)

    def scrollToBottom(self):
        self.widget.moveCursor(QTextCursor.End)
        self.widget.ensureCursorVisible()

class OperationThread(QThread): 
    finished = pyqtSignal() 

    def __init__(self, result_file, output_dir): 
        super().__init__() 
        self.result_file = result_file 
        self.output_dir = output_dir 

    def run(self): 
        do_operation(self.result_file, self.output_dir)
        self.finished.emit()

class FileBrowseApp(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.check_settings_file()
        self.logging = initialize_log()

    def initUI(self):
        self.setWindowTitle('WISDOM 設定ファイル準備ツール')
        self.setFixedSize(800, 400)
        self.centerWindow()

        label1 = QLabel('振分結果ファイル', self)
        label2 = QLabel('出力先フォルダ', self)

        self.txt_result_file = QLineEdit(self)
        self.txt_output_dir = QLineEdit(self)

        self.browseButton1 = QPushButton('参照', self)
        self.browseButton2 = QPushButton('参照', self)
        self.executeButton = QPushButton('実行', self)
        styleSubmitButton(self.executeButton)

        self.browseButton1.clicked.connect(self.browseFile)
        self.browseButton2.clicked.connect(self.browseFolder)
        self.executeButton.clicked.connect(self.executeAction)

        layout = QGridLayout()

        layout.addWidget(label1, 0, 0)
        layout.addWidget(self.txt_result_file, 0, 1)
        layout.addWidget(self.browseButton1, 0, 2)

        layout.addWidget(label2, 1, 0)
        layout.addWidget(self.txt_output_dir, 1, 1)
        layout.addWidget(self.browseButton2, 1, 2)

        layout.addWidget(self.executeButton, 2, 1, 1, 2)

        self.logTextEditLogger = QTextEditLogger(self)
        layout.addWidget(self.logTextEditLogger.widget, 3, 0, 1, 3)

        self.setLayout(layout)
        self.setupLogger()

    def setupLogger(self):
        initialize_log(additional_handlers=[self.logTextEditLogger])

    def centerWindow(self):
        qr = self.frameGeometry()
        cp = QDesktopWidget().availableGeometry().center()
        qr.moveCenter(cp)
        self.move(qr.topLeft())

    def browseFile(self):
        options = QFileDialog.Options()
        fileName, _ = QFileDialog.getOpenFileName(self, "Select File", "", "Excel File (*.xlsx)", options=options)
        if fileName:
            self.txt_result_file.setText(fileName)

    def browseFolder(self):
        options = QFileDialog.Options()
        folderName = QFileDialog.getExistingDirectory(self, "Select Folder", options=options)
        if folderName:
            self.txt_output_dir.setText(folderName)

    def executeAction(self):
        result_file = self.txt_result_file.text().strip()
        output_dir = self.txt_output_dir.text().strip()
        if result_file != "" and output_dir != "":
            self.executeButton.setEnabled(False) 
            self.browseButton1.setEnabled(False)
            self.browseButton2.setEnabled(False)
            self.thread = OperationThread(result_file, output_dir)
            self.thread.finished.connect(self.onOperationFinished)
            self.thread.start()
    
    def onOperationFinished(self):
        self.executeButton.setEnabled(True) 
        self.browseButton1.setEnabled(True)
        self.browseButton2.setEnabled(True)
        # QMessageBox.information(self, "ダウンロード完了", "すべてダウンロード完了しました。", QMessageBox.Ok)



    def check_settings_file(self):
        app_settings_path = f'./settings'
        settings_file = os.path.join(app_settings_path, "settings.json")
        config_file = os.path.join(app_settings_path, "config.json")

        if not os.path.exists(settings_file):
            QMessageBox.critical(None, "Error", "Error: settings file not found.", QMessageBox.Ok)
            sys.exit()

        if not os.path.exists(config_file):
            QMessageBox.critical(None, "Error", "Error: config  file not found.", QMessageBox.Ok)
            sys.exit() 
        

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = FileBrowseApp()
    ex.show()
    sys.exit(app.exec_())
