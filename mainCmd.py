import argparse
import os
import sys
# import json
import logging

from app.utils.utils import *
from app.basics.controller import *

logging = initialize_log()
if logging == 1:
    sys.exit(0)
logger = logging.getLogger(__name__)

def validate_file(file_path, extension):
    if not os.path.isfile(file_path):
        error_message = f"The file {file_path} does not exist."
        logging.error(error_message)
        return None
    if not file_path.endswith(extension):
        error_message = f"The file {file_path} is not a {extension} file."
        logging.error(error_message)
        return None
    return file_path

def validate_output_location(output_location):
    if not os.path.isdir(output_location):
        error_message = f"Create a new folder '{output_location}' ."
        logging.info(error_message)
        os.makedirs(output_location)
    return output_location

class MyArgumentParser(argparse.ArgumentParser):
    def error(self, message):
        sys.stderr.write(f'error: {message}\n')
        self.print_help()
        sys.exit(2)

def main():
    parser = MyArgumentParser(
        description='Provide parameters to process downloading.',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )


    parser.add_argument('-i', '--input', type=str, required=True, help='Input .xlsx file')
    parser.add_argument('-o', '--output', type=str, required=True, help='Output location directory')
    parser.add_argument('-c', '--config', type=str, required=False, help='Config .json file (optional)')

    try:
        args = parser.parse_args()
    except SystemExit as e:
        if e.code == 2:
            sys.exit(0)
        else:
            raise

    input_file = validate_file(args.input, '.xlsx')
    output_location = validate_output_location(args.output)
    config_file = validate_file(args.config, '.json') if args.config else None

    if input_file and output_location:
        do_operation(input_file, output_location, config_file)

    else:
        logger.error("Error in input parameters.")
        parser.print_help()

if __name__ == "__main__":
    main()