{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: Current File",
            "type": "debugpy",      
            "request": "launch",
            // "program": "${file}",
            "program": "mainCmd.py",
            "console": "integratedTerminal",
            // "args": ["-i", "C://GIT//GitHub//download_tool_file//input//振り分け結果-20240610.xlsx", 
            // "-o", "C://GIT//GitHub//download_tool_file//output", "-c", "C://GIT//GitHub//mdx-furiwake-tool//Furiwake-Tool//bin//Debug//settings//config.json"]
            // "args": ["-i", "C:/Users/<USER>/Desktop/liu/アオキ/最終化済みツール/成光精密/判断支援ツール_成光精密_20240621_01/test/振り分け結果-20241015.xlsx", 
            // "-o", "C:/Users/<USER>/Desktop/アオキ確認1129/出力", "-c", "C:/Users/<USER>/Desktop/liu/アオキ/code/判断支援ツール/master/dev_master/mdx-furiwake-tool/Furiwake-Tool/bin/Debug/settings-成光/config.json"]
            "args": ["-i", "C:/Users/<USER>/Desktop/temp/250620DLツールテスト/振り分け結果_envsOverWrite-TEST 1.xlsx", 
            "-o", "C:/Users/<USER>/Desktop/temp/250620DLツールテスト/", "-c", "C:/Users/<USER>/Desktop/liu/code/DLツール/mdx-meviy-downloader/config_operator使うときはこちら.json"]
        
        }
    ]
}