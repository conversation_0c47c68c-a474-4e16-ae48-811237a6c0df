import sys
import logging
from app.basics.operations import *
from app.utils.utils import *

logger = logging.getLogger(__name__)

def do_operation(result_file, output_dir, login_config_file = None):
    session = create_session(login_config_file)
    if session == None:
        logger.error("Authentication Failed!! Check your credentials.")
        sys.exit()

    logger.info(f"------INITIATE NEW DOWNLOAD REQUEST------")
    status = start_download_execution(result_file, output_dir)
    if status == 0:
        logger.info(f"------OPERATION COMPLETED------")